import SwiftUI
import AVFoundation
import Combine
import Foundation
import MediaPipeTasksVision
import CoreMedia

// TODO: 请将此 DebugLogger 类移到单独的文件中（/FitCount/Utils/DebugLogger.swift）
// 并使用 Xcode 的“添加文件到目标”功能将其加入到项目中
// File -> Add Files to "FitCount"... -> 选择 Utils/DebugLogger.swift 文件 -> Add
// 调试日志工具类 - 帮助我们记录关键事件

// MARK: - 重构说明
//
// 本文件已经重构，将大型组件拆分为更小、更易维护的文件：
//
// 1. 人体姿态绘制组件 → FitCount/Views/Components/PoseOverlayView.swift
// 2. 相机管理器 → FitCount/Views/Components/CameraManager.swift
// 3. 相机管理器扩展 → FitCount/Views/Components/CameraManager+Extensions.swift
// 4. 相机预览组件 → FitCount/Views/Components/CameraPreview.swift
// 5. 底部设置面板 → FitCount/Views/Components/BottomSheetView.swift
// 6. 视图扩展和工具类 → FitCount/Views/Components/ViewExtensions.swift
//
// 这样的重构提供了以下好处：
// - 更好的代码组织和可维护性
// - 更容易进行单元测试
// - 更清晰的职责分离
// - 更容易复用组件

// 主视图
struct ExerciseSitupView: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.horizontalSizeClass) var horizontalSizeClass
    var exerciseName: String

    // 状态管理
    @StateObject private var cameraManager = CameraManager()
    @State private var showAlertMessage = false
    @State private var alertMessage = ""
    @State private var showSettings = false // 控制设置面板的显示

    // 初始化
    init(exerciseName: String) {
        self.exerciseName = exerciseName
        DebugLogger.info("FirstDetailView初始化，exerciseName=\(exerciseName)")
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景部分：相机预览或黑色背景
                if let previewLayer = cameraManager.preview {
                    // 显示相机预览
                    ZStack {
                        CameraPreview(previewLayer: previewLayer)
                            .edgesIgnoringSafeArea(.all)
                            .onAppear {
                                DebugLogger.debug("CameraPreview视图出现")
                            }
                            .onDisappear {
                                DebugLogger.debug("CameraPreview视图消失")
                            }

                        // 叠加人体关键点绘制视图
                        if let poseOverlayView = cameraManager.poseOverlayView {
                            PoseOverlayViewRepresentable(poseOverlayView: poseOverlayView)
                                .edgesIgnoringSafeArea(.all)
                                .allowsHitTesting(false) // 不拦截触摸事件
                        }
                    }
                } else {
                    // 显示黑色背景和状态信息
                    Color.black.edgesIgnoringSafeArea(.all)

                        .overlay(
                            VStack(spacing: 20) {
                                Text("摄像头初始化中...")
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .padding()

                                // 显示详细状态
                                Text(cameraManager.debugState)
                                    .foregroundColor(.yellow)
                                    .padding()
                                    .background(Color.black.opacity(0.5))
                                    .cornerRadius(10)

                                // 显示错误信息（如果有）
                                if let error = cameraManager.sessionError {
                                    Text("错误: " + error)
                                        .foregroundColor(.red)
                                        .multilineTextAlignment(.center)
                                        .padding()
                                        .background(Color.black.opacity(0.5))
                                        .cornerRadius(10)
                                }

                                // 检查会话状态
                                if cameraManager.preview != nil && !cameraManager.isSessionRunning {
                                    Button(action: {
                                        DebugLogger.debug("用户点击尝试重新启动相机")
                                        cameraManager.startSession()
                                    }) {
                                        Text("重新启动相机")
                                            .foregroundColor(.white)
                                            .padding()
                                            .background(Color.blue)
                                            .cornerRadius(10)
                                    }
                                }

                                // 调试按钮 - 在开发阶段使用
#if DEBUG
                                Button(action: {
                                    DebugLogger.debug("用户点击检查相机状态")
                                    cameraManager.checkCameraStatus()
                                }) {
                                    Text("检查相机状态")
                                        .foregroundColor(.white)
                                        .padding()
                                        .background(Color.green)
                                        .cornerRadius(10)
                                }
#endif
                            }
                        )
                }

                // 如果相机中断，显示提示信息
                if cameraManager.isSessionInterrupted {
                    Text("相机访问已中断")
                        .font(.title)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(10)
                }

                // 主界面结构：顶部控制栏和底部设置面板
                VStack(spacing: 0) {
                    // 顶部区域
                    HStack {
                        Text(exerciseName)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding()

                        Spacer()

                        // 切换摄像头按钮
                        Button(action: {
                            DebugLogger.debug("用户点击切换摄像头")
                            cameraManager.toggleCamera()
                        }) {
                            Image(systemName: "camera.rotate.fill")
                                .font(.title)
                                .foregroundColor(.white)
                                .padding()
                        }
                    }
                    .background(Color.black.opacity(0.3))

                    // 仰卧起坐计数显示区域
                    VStack(spacing: 10) {
                        // 计数显示
                        Text(String(format: NSLocalizedString("situp_count_label", comment: "仰卧起坐: %d"), cameraManager.sitUpCounter.count))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal)
                            .padding(.vertical, 8)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(12)

                        // 角度显示
                        Text(String(format: NSLocalizedString("situp_angle_label", comment: "角度: %.1f°"), cameraManager.sitUpCounter.currentAngle))
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal)
                            .padding(.vertical, 6)
                            .background(Color.black.opacity(0.5))
                            .cornerRadius(8)

                        // 状态显示
                        Text(String(format: NSLocalizedString("situp_status_label", comment: "状态: %@"), cameraManager.sitUpCounter.statusText))
                            .font(.title3)
                            .fontWeight(.medium)
                            .foregroundColor(.yellow)
                            .padding(.horizontal)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.4))
                            .cornerRadius(6)

                        // 提示信息
                        Text(cameraManager.sitUpCounter.hintText)
                            .font(.body)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.3))
                            .cornerRadius(6)
                    }
                    .padding(.top, 20)
                    .padding(.horizontal, 20)

                    // 中间区域 - 占满所有可用空间
                    Spacer()
                }
                .edgesIgnoringSafeArea(.bottom) // 关键修改：确保面板延伸到底部

                // 我们使用.sheet展示设置面板，不再需要半透明遮罩层
            }
            .ignoresSafeArea(.keyboard) // 忽略键盘安全区域
            // 屏幕方向自适应（方法已移动到ViewExtensions.swift）
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        // 底部可拖拽设置面板
        .sheet(isPresented: $showSettings) {
            NavigationView {
                VStack(spacing: 0) {
                    // 拖拽指示器
                    Capsule()
                        .fill(Color.gray.opacity(0.4))
                        .frame(width: 40, height: 5)
                        .padding(.top, 8)

                    // 设置内容
                    List {
                        Section {
                            Toggle(isOn: .constant(true)) {
                                Text("启用声音")
                            }
                            Toggle(isOn: .constant(false)) {
                                Text("显示计数器")
                            }
                            Button(action: {
                                // 重置仰卧起坐计数器
                                cameraManager.sitUpCounter.reset()
                                DebugLogger.info("用户重置了仰卧起坐计数器")
                            }) {
                                Text("重置计数器")
                                    .foregroundColor(.red)
                            }
                        } header: {
                            Text("通用设置")
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                }
                .navigationTitle("设置")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            showSettings = false
                        }
                    }
                }
            }
            .presentationDetents([.medium, .large])
            .presentationDragIndicator(.visible)
        }
        .navigationTitle(exerciseName)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    DebugLogger.debug("用户点击返回按钮")
                    dismiss()
                } label: {
                    Text("返回")
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button {
                    showSettings = true
                } label: {
                    Image(systemName: "slider.horizontal.3")
                }
            }
        }
        .onAppear {
            DebugLogger.info("FirstDetailView显示")
            cameraManager.setupAndStartSession()
        }
        .onDisappear {
            DebugLogger.info("FirstDetailView消失")
            cameraManager.stopSession()
        }
        .alert(isPresented: $showAlertMessage) {
            Alert(
                title: Text("相机错误"),
                message: Text(alertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .onChange(of: cameraManager.sessionError) { errorMessage in
            if let errorMessage = errorMessage {
                DebugLogger.error("收到相机错误: \(errorMessage)")
                alertMessage = errorMessage
                showAlertMessage = true
            }
        }
    }
}

// 视图扩展和工具类已移动到 FitCount/Views/Components/ViewExtensions.swift

struct ExerciseSitupView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ExerciseSitupView(exerciseName: "仰卧起坐")
        }
        .environment(\.locale, .init(identifier: "zh-Hans"))
    }
}
