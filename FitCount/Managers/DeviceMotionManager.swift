import Foundation
import CoreMotion
import UIKit

/// 设备运动管理器
/// 负责获取和处理设备的陀螺仪、加速度计等运动数据
class DeviceMotionManager: ObservableObject {

    // MARK: - 发布属性

    /// 当前设备姿态数据
    @Published var deviceAttitude: CMAttitude?

    /// 当前重力向量（在设备坐标系中）
    @Published var gravityVector: CMAcceleration?

    /// 设备运动管理器是否可用
    @Published var isAvailable: Bool = false

    /// 设备运动管理器是否正在运行
    @Published var isActive: Bool = false

    // MARK: - 私有属性

    /// CoreMotion 管理器
    private let motionManager = CMMotionManager()

    /// 运动数据更新队列
    private let motionQueue = OperationQueue()

    /// 更新频率（Hz）
    private let updateInterval: TimeInterval = 1.0 / 60.0 // 60 FPS

    /// 重力向量平滑滤波器
    private var gravityFilter = LowPassFilter(alpha: 0.1)

    // MARK: - 初始化

    init() {
        setupMotionManager()
        checkAvailability()
    }

    deinit {
        stopMotionUpdates()
    }

    // MARK: - 公共方法

    /// 开始运动数据更新
    func startMotionUpdates() {
        guard motionManager.isDeviceMotionAvailable && !isActive else {
            DebugLogger.warning("设备运动数据不可用或已经在运行")
            return
        }

        DebugLogger.info("开始设备运动数据更新")

        // 选择最佳的参考坐标系
        let referenceFrame: CMAttitudeReferenceFrame
        if CMMotionManager.availableAttitudeReferenceFrames().contains(.xMagneticNorthZVertical) {
            referenceFrame = .xMagneticNorthZVertical
        } else if CMMotionManager.availableAttitudeReferenceFrames().contains(.xArbitraryZVertical) {
            referenceFrame = .xArbitraryZVertical
        } else {
            referenceFrame = .xArbitraryZVertical // 默认值
        }

        motionManager.startDeviceMotionUpdates(using: referenceFrame, to: motionQueue) { [weak self] (motion, error) in
            guard let self = self, let motion = motion else {
                if let error = error {
                    DebugLogger.error("设备运动数据更新错误: \(error.localizedDescription)")
                }
                return
            }

            self.processMotionData(motion)
        }

        DispatchQueue.main.async { [weak self] in
            self?.isActive = true
        }
    }

    /// 停止运动数据更新
    func stopMotionUpdates() {
        guard isActive else { return }

        DebugLogger.info("停止设备运动数据更新")
        motionManager.stopDeviceMotionUpdates()

        DispatchQueue.main.async { [weak self] in
            self?.isActive = false
        }
    }

    /// 获取当前的精确重力向量（在 MediaPipe 坐标系中）
    /// - Returns: 重力向量的 3D 坐标，如果数据不可用则返回 nil
    func getPreciseGravityVector() -> (x: Float, y: Float, z: Float)? {
        guard let gravity = gravityVector else {
            DebugLogger.warning("重力向量数据不可用")
            return nil
        }

        // 关键理解：MediaPipe worldLandmarks 坐标系是相对于相机传感器的
        // 无论设备如何旋转，相机传感器看到的世界坐标系都是一样的
        //
        // MediaPipe worldLandmarks 坐标系（相机坐标系）：
        // - X轴：相机视角的右侧为正
        // - Y轴：相机视角的上方为正
        // - Z轴：相机视角的前方为正（远离相机）
        //
        // 重力在相机坐标系中始终向下，即 Y轴负方向
        // 我们需要将 CoreMotion 的重力向量转换到这个固定的相机坐标系

        let currentOrientation = UIDevice.current.orientation
        let convertedGravity = convertGravityToMediaPipeCoordinates(
            gravity: gravity,
            deviceOrientation: currentOrientation
        )

        // 详细调试日志（降低频率）
        if Int(Date().timeIntervalSince1970 * 5) % 5 == 0 { // 每5秒输出一次
            DebugLogger.debug("设备方向: \(deviceOrientationString(currentOrientation))")
            DebugLogger.debug("原始重力向量（设备坐标系）: (\(String(format: "%.3f", gravity.x)), \(String(format: "%.3f", gravity.y)), \(String(format: "%.3f", gravity.z)))")
            DebugLogger.debug("转换后重力向量（相机坐标系）: (\(String(format: "%.3f", convertedGravity.x)), \(String(format: "%.3f", convertedGravity.y)), \(String(format: "%.3f", convertedGravity.z)))")
        }

        return convertedGravity
    }

    /// 获取设备当前的倾斜角度信息
    /// - Returns: 包含 pitch、roll、yaw 的元组
    func getDeviceAngles() -> (pitch: Double, roll: Double, yaw: Double)? {
        guard let attitude = deviceAttitude else { return nil }

        return (
            pitch: attitude.pitch * 180.0 / .pi,  // 俯仰角（度）
            roll: attitude.roll * 180.0 / .pi,    // 翻滚角（度）
            yaw: attitude.yaw * 180.0 / .pi       // 偏航角（度）
        )
    }

    // MARK: - 私有方法

    /// 设置运动管理器
    private func setupMotionManager() {
        motionQueue.name = "DeviceMotionQueue"
        motionQueue.maxConcurrentOperationCount = 1

        motionManager.deviceMotionUpdateInterval = updateInterval

        // 注意：attitudeReferenceFrame 是只读属性，在 startDeviceMotionUpdates 时设置

        DebugLogger.debug("设备运动管理器配置完成，更新频率: \(1.0/updateInterval) Hz")
    }

    /// 检查设备运动功能可用性
    private func checkAvailability() {
        let available = motionManager.isDeviceMotionAvailable

        DispatchQueue.main.async { [weak self] in
            self?.isAvailable = available
        }

        if available {
            DebugLogger.info(NSLocalizedString("device_motion_data_available", comment: "设备运动数据可用"))
        } else {
            DebugLogger.warning(NSLocalizedString("device_motion_data_unavailable", comment: "设备运动数据不可用"))
        }
    }

    /// 处理运动数据
    /// - Parameter motion: CoreMotion 设备运动数据
    private func processMotionData(_ motion: CMDeviceMotion) {
        // 获取重力向量并应用低通滤波
        let rawGravity = motion.gravity
        let filteredGravity = gravityFilter.filter(rawGravity)

        // 获取设备姿态
        let attitude = motion.attitude

        // 在主线程更新发布属性
        DispatchQueue.main.async { [weak self] in
            self?.gravityVector = filteredGravity
            self?.deviceAttitude = attitude
        }

        // 调试输出（降低频率）
        if Int(Date().timeIntervalSince1970 * 10) % 10 == 0 { // 每秒输出一次
            let angles = (
                pitch: attitude.pitch * 180.0 / .pi,
                roll: attitude.roll * 180.0 / .pi,
                yaw: attitude.yaw * 180.0 / .pi
            )

            DebugLogger.debug("设备姿态 - Pitch: \(String(format: "%.1f", angles.pitch))°, Roll: \(String(format: "%.1f", angles.roll))°, Yaw: \(String(format: "%.1f", angles.yaw))°")
            DebugLogger.debug("重力向量 - X: \(String(format: "%.3f", filteredGravity.x)), Y: \(String(format: "%.3f", filteredGravity.y)), Z: \(String(format: "%.3f", filteredGravity.z))")
        }
    }

    /// 将 CoreMotion 重力向量转换为相机坐标系
    ///
    /// 关键理解：
    /// - MediaPipe worldLandmarks 始终相对于相机坐标系，不受设备旋转影响
    /// - CoreMotion 重力向量是在设备坐标系中的
    /// - 需要将设备坐标系的重力向量转换到相机坐标系
    /// - 相机坐标系的方向取决于设备方向
    ///
    /// - Parameters:
    ///   - gravity: CoreMotion 重力向量（设备坐标系）
    ///   - deviceOrientation: 设备方向
    /// - Returns: 相机坐标系中的重力向量
    private func convertGravityToMediaPipeCoordinates(
        gravity: CMAcceleration,
        deviceOrientation: UIDeviceOrientation
    ) -> (x: Float, y: Float, z: Float) {

        // CoreMotion 设备坐标系：
        // - X轴：设备右侧为正
        // - Y轴：设备顶部为正
        // - Z轴：设备屏幕向外为正
        //
        // 相机坐标系（MediaPipe worldLandmarks）：
        // - X轴：相机视角的右侧为正
        // - Y轴：相机视角的上方为正
        // - Z轴：相机视角的前方为正（远离相机）
        //
        // 需要根据设备方向将设备坐标系转换到相机坐标系

        switch deviceOrientation {
        case .portrait:
            // 竖屏：相机与设备方向一致
            // 设备X -> 相机X，设备Y -> 相机Y，设备Z -> 相机Z
            return (
                x: Float(gravity.x),   // 设备左右 -> 相机左右
                y: Float(-gravity.y),  // 设备上下反转 -> 相机上下（重力向下）
                z: Float(gravity.z)    // 设备前后 -> 相机前后
            )

        case .portraitUpsideDown:
            // 倒置竖屏：相机相对设备旋转180度
            // 设备X -> 相机-X，设备Y -> 相机-Y，设备Z -> 相机Z
            return (
                x: Float(-gravity.x),  // 设备左右反转 -> 相机左右
                y: Float(gravity.y),   // 设备上下 -> 相机上下
                z: Float(gravity.z)    // 设备前后 -> 相机前后
            )

        case .landscapeLeft:
            // 左横屏：相机相对设备逆时针旋转90度
            // 设备X -> 相机-Y，设备Y -> 相机X，设备Z -> 相机Z
            return (
                x: Float(-gravity.y),  // 设备上下反转 -> 相机左右
                y: Float(gravity.x),   // 设备左右 -> 相机上下
                z: Float(gravity.z)    // 设备前后 -> 相机前后
            )

        case .landscapeRight:
            // 右横屏：相机相对设备顺时针旋转90度
            // 设备X -> 相机Y，设备Y -> 相机-X，设备Z -> 相机Z
            return (
                x: Float(gravity.y),   // 设备上下 -> 相机左右
                y: Float(-gravity.x),  // 设备左右反转 -> 相机上下
                z: Float(gravity.z)    // 设备前后 -> 相机前后
            )

        default:
            // 默认情况（包括 .unknown, .faceUp, .faceDown）：使用竖屏转换
            return (
                x: Float(gravity.x),
                y: Float(-gravity.y),
                z: Float(gravity.z)
            )
        }
    }

    /// 获取设备方向的字符串描述
    /// - Parameter orientation: 设备方向
    /// - Returns: 方向的字符串描述
    private func deviceOrientationString(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "屏幕向上"
        case .faceDown:
            return "屏幕向下"
        case .unknown:
            return "未知方向"
        @unknown default:
            return "未知方向"
        }
    }
}

// MARK: - 低通滤波器

/// 低通滤波器，用于平滑传感器数据
private struct LowPassFilter {
    private let alpha: Double
    private var previousValue: CMAcceleration?

    init(alpha: Double) {
        self.alpha = alpha
    }

    mutating func filter(_ newValue: CMAcceleration) -> CMAcceleration {
        guard let previous = previousValue else {
            previousValue = newValue
            return newValue
        }

        let filtered = CMAcceleration(
            x: alpha * newValue.x + (1.0 - alpha) * previous.x,
            y: alpha * newValue.y + (1.0 - alpha) * previous.y,
            z: alpha * newValue.z + (1.0 - alpha) * previous.z
        )

        previousValue = filtered
        return filtered
    }
}
