//
//  CoordinateSystemTestHelper.swift
//  FitCount
//
//  Created by AI Assistant on 2025-05-27.
//  用于测试和验证坐标系转换的辅助工具
//

import Foundation
import UIKit
import CoreMotion

/// 坐标系转换测试辅助工具
/// 用于验证 DeviceMotionManager 中的坐标转换逻辑是否正确
class CoordinateSystemTestHelper {
    
    /// 测试不同设备方向下的重力向量转换
    /// 这个方法模拟在不同设备方向下，相同的物理重力向量应该转换为相同的相机坐标系重力向量
    static func testGravityVectorConsistency() {
        DebugLogger.info("开始坐标系转换一致性测试")
        
        // 模拟标准重力向量（设备竖屏时，重力向下）
        let standardGravity = CMAcceleration(x: 0.0, y: -1.0, z: 0.0)
        
        // 测试所有设备方向
        let orientations: [UIDeviceOrientation] = [
            .portrait,
            .portraitUpsideDown,
            .landscapeLeft,
            .landscapeRight
        ]
        
        var results: [(UIDeviceOrientation, (x: Float, y: Float, z: Float))] = []
        
        for orientation in orientations {
            // 根据设备方向调整输入的重力向量
            // 这模拟了当设备旋转时，CoreMotion 报告的重力向量如何变化
            let deviceGravity = getDeviceGravityForOrientation(orientation)
            
            // 使用 DeviceMotionManager 的转换逻辑
            let convertedGravity = convertGravityToMediaPipeCoordinates(
                gravity: deviceGravity,
                deviceOrientation: orientation
            )
            
            results.append((orientation, convertedGravity))
            
            DebugLogger.info("设备方向: \(orientationString(orientation))")
            DebugLogger.info("  输入重力向量（设备坐标系）: (\(String(format: "%.3f", deviceGravity.x)), \(String(format: "%.3f", deviceGravity.y)), \(String(format: "%.3f", deviceGravity.z)))")
            DebugLogger.info("  输出重力向量（相机坐标系）: (\(String(format: "%.3f", convertedGravity.x)), \(String(format: "%.3f", convertedGravity.y)), \(String(format: "%.3f", convertedGravity.z)))")
        }
        
        // 验证一致性
        validateConsistency(results)
    }
    
    /// 根据设备方向获取对应的设备坐标系重力向量
    /// 这模拟了当设备旋转时，CoreMotion 如何报告重力向量
    private static func getDeviceGravityForOrientation(_ orientation: UIDeviceOrientation) -> CMAcceleration {
        switch orientation {
        case .portrait:
            // 竖屏：重力向设备下方（Y轴负方向）
            return CMAcceleration(x: 0.0, y: -1.0, z: 0.0)
            
        case .portraitUpsideDown:
            // 倒置竖屏：重力向设备上方（Y轴正方向）
            return CMAcceleration(x: 0.0, y: 1.0, z: 0.0)
            
        case .landscapeLeft:
            // 左横屏：重力向设备右侧（X轴正方向）
            return CMAcceleration(x: 1.0, y: 0.0, z: 0.0)
            
        case .landscapeRight:
            // 右横屏：重力向设备左侧（X轴负方向）
            return CMAcceleration(x: -1.0, y: 0.0, z: 0.0)
            
        default:
            return CMAcceleration(x: 0.0, y: -1.0, z: 0.0)
        }
    }
    
    /// 复制 DeviceMotionManager 中的坐标转换逻辑用于测试
    private static func convertGravityToMediaPipeCoordinates(
        gravity: CMAcceleration,
        deviceOrientation: UIDeviceOrientation
    ) -> (x: Float, y: Float, z: Float) {
        
        switch deviceOrientation {
        case .portrait:
            return (
                x: Float(gravity.x),
                y: Float(-gravity.y),
                z: Float(gravity.z)
            )
            
        case .portraitUpsideDown:
            return (
                x: Float(-gravity.x),
                y: Float(gravity.y),
                z: Float(gravity.z)
            )
            
        case .landscapeLeft:
            return (
                x: Float(-gravity.y),
                y: Float(gravity.x),
                z: Float(gravity.z)
            )
            
        case .landscapeRight:
            return (
                x: Float(gravity.y),
                y: Float(-gravity.x),
                z: Float(gravity.z)
            )
            
        default:
            return (
                x: Float(gravity.x),
                y: Float(-gravity.y),
                z: Float(gravity.z)
            )
        }
    }
    
    /// 验证转换结果的一致性
    private static func validateConsistency(_ results: [(UIDeviceOrientation, (x: Float, y: Float, z: Float))]) {
        DebugLogger.info("验证转换结果一致性...")
        
        // 期望的结果：所有方向下转换后的重力向量都应该是 (0, -1, 0)
        // 因为在相机坐标系中，重力始终向下（Y轴负方向）
        let expectedGravity = (x: Float(0.0), y: Float(-1.0), z: Float(0.0))
        let tolerance: Float = 0.001
        
        var allConsistent = true
        
        for (orientation, gravity) in results {
            let xDiff = abs(gravity.x - expectedGravity.x)
            let yDiff = abs(gravity.y - expectedGravity.y)
            let zDiff = abs(gravity.z - expectedGravity.z)
            
            let isConsistent = xDiff < tolerance && yDiff < tolerance && zDiff < tolerance
            
            if isConsistent {
                DebugLogger.info("✅ \(orientationString(orientation)): 转换正确")
            } else {
                DebugLogger.error("❌ \(orientationString(orientation)): 转换不正确")
                DebugLogger.error("  期望: (\(expectedGravity.x), \(expectedGravity.y), \(expectedGravity.z))")
                DebugLogger.error("  实际: (\(gravity.x), \(gravity.y), \(gravity.z))")
                DebugLogger.error("  差异: (\(xDiff), \(yDiff), \(zDiff))")
                allConsistent = false
            }
        }
        
        if allConsistent {
            DebugLogger.info("🎉 所有设备方向的坐标转换都正确！")
        } else {
            DebugLogger.error("⚠️ 存在坐标转换错误，需要修复")
        }
    }
    
    /// 获取设备方向的字符串描述
    private static func orientationString(_ orientation: UIDeviceOrientation) -> String {
        switch orientation {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "屏幕向上"
        case .faceDown:
            return "屏幕向下"
        case .unknown:
            return "未知方向"
        @unknown default:
            return "未知方向"
        }
    }
}
